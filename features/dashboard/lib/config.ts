export const basePath = "/dashboard";

export const customNavItems = [
  {
    title: 'Orders',
    href: '/platform/orders',
    color: 'blue',
    description: 'Manage customer orders, track fulfillment, and process payments.'
  },
  {
    title: 'Products',
    href: '/platform/products',
    color: 'green',
    description: 'Create and manage your product catalog, pricing, and inventory.'
  },
  {
    title: 'Categories',
    href: '/platform/product-categories',
    color: 'lime', // Example color, can be adjusted
    description: 'Organize products into categories and manage their hierarchy.'
  },
  {
    title: 'Users',
    href: '/platform/users',
    color: 'purple',
    description: 'Manage customer accounts, profiles, and user permissions.'
  },
  {
    title: 'Discounts',
    href: '/platform/discounts',
    color: 'pink',
    description: 'Create promotional codes, sales campaigns, and discount rules.'
  },
  {
    title: 'Gift Cards',
    href: '/platform/gift-cards',
    color: 'orange',
    description: 'Issue and manage gift cards for customer purchases and rewards.'
  },
  {
    title: 'Price Lists',
    href: '/platform/price-lists',
    color: 'emerald',
    description: 'Configure pricing strategies and customer-specific price tiers.'
  },
  {
    title: 'Draft Orders',
    href: '/platform/draft-orders',
    color: 'stone',
    description: 'Create and manage draft orders before finalizing customer purchases.'
  },
  {
    title: 'Analytics',
    href: '/platform/analytics',
    color: 'indigo',
    description: 'View sales reports, customer insights, and business performance metrics.'
  },
  {
    title: 'Batch Jobs',
    href: '/platform/batch-jobs',
    color: 'cyan',
    description: 'Monitor and manage background tasks and bulk operations.'
  },
  {
    title: 'Returns',
    href: '/platform/returns',
    color: 'red',
    description: 'Process customer returns, refunds, and exchange requests.'
  },
  {
    title: 'Claims',
    href: '/platform/claims',
    color: 'rose',
    description: 'Handle customer claims, disputes, and resolution processes.'
  },
  {
    title: 'Inventory',
    href: '/platform/inventory',
    color: 'teal',
    description: 'Track stock levels, manage warehouses, and monitor inventory.'
  },
  {
    title: 'Shipping',
    href: '/platform/shipping',
    color: 'sky',
    description: 'Configure shipping options, rates, and fulfillment methods.'
  },
  {
    title: 'Settings',
    href: '/platform/settings',
    color: 'zinc',
    description: 'Configure platform settings, integrations, and system preferences.'
  }
]; 
import React from "react";
import Link from "next/link";
import {
  B<PERSON><PERSON>rumb,
  Bread<PERSON>rumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { ModelSwitcherDropdown } from "./ModelSwitcherDropdown";
import { basePath, customNavItems } from "@/features/dashboard/lib/config";

interface BreadcrumbItem {
  type: "link" | "model" | "page";
  label: string;
  href?: string;
  showModelSwitcher?: boolean;
  switcherType?: "model" | "platform";
}

interface PageBreadcrumbsProps {
  items: BreadcrumbItem[];
  actions?: React.ReactNode;
}

export function PageBreadcrumbs({ items, actions }: PageBreadcrumbsProps) {
  return (
    <header className="sticky top-0 z-30 bg-background flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12 border-b">
      <div className="flex items-center gap-2 px-4 w-full justify-between">
        <div className="flex items-center gap-3">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb className="hidden md:flex">
            <BreadcrumbList>
              {items.map((item, index) => (
                <React.Fragment key={index}>
                  {index > 0 && <BreadcrumbSeparator />}
                  <BreadcrumbItem>
                    {item.type === "link" && (
                      <BreadcrumbLink asChild>
                        <Link href={`${basePath}${item.href}`}>{item.label}</Link>
                      </BreadcrumbLink>
                    )}
                    {item.type === "model" && (
                      <div className="flex items-center gap-3">
                        <BreadcrumbLink asChild>
                          <Link href={`${basePath}${item.href}`}>{item.label}</Link>
                        </BreadcrumbLink>
                        {item.showModelSwitcher && (
                          <ModelSwitcherDropdown 
                            type="model"
                            items={customNavItems}
                            basePath={basePath}
                          />
                        )}
                      </div>
                    )}
                    {item.type === "page" && (
                      <div className="flex items-center gap-3">
                        {item.showModelSwitcher ? (
                          <ModelSwitcherDropdown
                            type={item.switcherType || "model"}
                            title={item.label}
                            items={customNavItems}
                            basePath={basePath}
                          />
                        ) : (
                          <BreadcrumbPage>{item.label}</BreadcrumbPage>
                        )}
                      </div>
                    )}
                  </BreadcrumbItem>
                </React.Fragment>
              ))}
            </BreadcrumbList>
          </Breadcrumb>
        </div>
        {actions && (
          <div className="flex items-center gap-3">
            {actions}
          </div>
        )}
      </div>
    </header>
  );
}